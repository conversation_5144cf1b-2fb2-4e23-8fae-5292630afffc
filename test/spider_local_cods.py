import time
from urllib3 import disable_warnings
from resx.log import setup_logger
from resx.mysql_client import MySQLClient
from resx.config import *
import re
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_cods import CodsCrawler as Crawler
from eventlog_spider.parser.parser_cods import CodsParser as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

disable_warnings()
mysql = MySQLClient(**CFG_MYSQL_GS_OUTER)
history_name_dao = MySQLClient(**CFG_MYSQL_GS_OUTER)

os.environ['POD_ENV'] = 'online'


def get_eventlog(name):
    return Eventlog.from_dict(
        {
            "event_id": "octopus_entry-FObCfcjZ-fe248d917716d2de1e85b4cc9153a994-cods-1754903739525",
            "code": -1,
            "selector": {
                "reason": "normal_schedule",
                "crawler_id": "FObCfcjZ",
                "crawler_name": "cods",
                "dimension_name": "新机构",
                "score": 1.175490374,
                "from_cache": False,
                "receive_time": "2025-08-11 17:15:39",
                "send_time": "2025-08-11 17:15:39",
                "try_id": 0,
                "info": {
                    "unified_social_credit_code": name,
                    "org_name": "重庆市南川区福寿镇打鼓村村民委员会"
                }
            },
            "crawler": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "parser": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "fusion": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "channel": {},
            "dimensions": {}
        }
    )


crawler = Crawler()
parser = Parser()

list_ = ['54511623C41843861G']
temp = []
for i in list_:
    # if i.startswith('11'):
    #     temp.append(i)
    #     continue
    a = get_eventlog(i)
    crawler.crawl(a)
    parser.parse(a)
    logger.info(f'after parser {a.model_dump_json()}')
print(temp)
