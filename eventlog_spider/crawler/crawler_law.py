from hashlib import md5
from requests import Session
import json
import re
import random
import urllib3
import os
from bs4 import BeautifulSoup
import time
from fontTools.ttLib import TTFont
import string
from urllib.parse import urlparse, quote
from io import BytesIO
import requests
import execjs
import copy
import uuid

from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, CrawlerTools, MyException
from resx.func import cur_ts_sec
from resx.redis_types import Redis, RedisQueue
from resx.config import *
from resx.log import setup_logger

logger = setup_logger(name=__name__)

urllib3.disable_warnings()
parent_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


class CrawlerLawTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)

        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class CrawlerLaw(C<PERSON>ler, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'law'

    def __init__(self, **kwargs):
        super().__init__(input_queue=RedisQueue(name='law_firm', **CFG_REDIS_GS, db=9), task_cls=CrawlerLawTask, eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: CrawlerLawTask = self.get_crawler_task()
        task.pages = {}
        eventlog: Eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = StatusCode.GIVE_UP

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} --> 搜索为空')
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if e.message == '接口连续失败':
                logger.warning(f'{task.keyword} --> 接口连续失败')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
        except Exception as e:
            raise e

    def crawl_(self, task: CrawlerLawTask):
        acla = Acla()
        law = Law()
        for _ in range(3):
            try:
                html, legalPerson_ = acla.crawl_detail(task.keyword)
                law_info, legalPerson = acla.parse(html, '#allow li')
                legalPerson = legalPerson if legalPerson else legalPerson_

                session = self.get_new_session()
                law_info2, lawyers = {}, {}
                list_: list = law.crawl_first(session, law_info['name'], xzqh=law_info["code"][2:4])
                if list_:
                    info = law.crawl_detail_12348(session, list_[0])
                    law_info2 = law.parse_page(info)
                    lawyers = law.crawl_lawyers(session, list_[0])

                law_info = acla.parse_map_law(law_info, legalPerson)
                lawyer_list, lawyer_list2 = law.parse_lawyers(lawyers)
                return {
                    'law_info.json': json.dumps(law_info, ensure_ascii=False),
                    'law_info2.json': json.dumps(law_info2, ensure_ascii=False),
                    'lawyer_list.json': json.dumps(lawyer_list, ensure_ascii=False),
                    'lawyer_list2.json': json.dumps(lawyer_list2, ensure_ascii=False),
                }
            except MyException as e:
                raise e
            except Exception as e:
                logger.error(f'{type(e)}-{task.keyword}: {_} - {self.custom_traceback(e)}')


class Law(CrawlerTools):
    link = "http://www.12348.gov.cn/lawerdeptlist/getlawerdeptlist"
    code_dic = {'12': '天津', '13': '河北', '14': '山西', '15': '内蒙古自治区', '21': '辽宁', '22': '吉林',
                '23': '黑龙江', '31': '上海', '32': '江苏', '33': '浙江', '34': '安徽', '35': '福建', '36': '江西', '37': '山东',
                '41': '河南', '42': '湖北', '43': '湖南', '44': '广东', '45': '广西壮族自治区', '46': '海南', '50': '重庆',
                '51': '四川', '52': '贵州', '53': '云南', '54': '西藏自治区', '61': '陕西', '62': '甘肃', '63': '青海',
                '64': '宁夏回族自治区', '65': '新疆维吾尔自治区', '66': '新疆生产建设兵团', '11': '北京'}

    def __init__(self):
        super().__init__()
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/json;charset=UTF-8",
            "DNT": "1",
            "Origin": "https://www.12348.gov.cn",
            "Pragma": "no-cache",
            "Referer": "https://www.12348.gov.cn/",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
            "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\""
        }
        self.timeout = 10

    def crawl_first(self, session: Session, keyword='', xzqh='11'):
        v_key = str(uuid.uuid4())
        crawlerCookie = str(uuid.uuid4())
        guid = str(uuid.uuid4())
        url = "https://www.12348.gov.cn/lawerdeptlist/getlawerdeptlist"
        data = {
            "pageSize": 12,
            "pageNum": 1,
            "xzqh": xzqh,
            "lsswsmc": keyword,
            "yw": "",
            "pzslsj": 0,
            "nums": 0,
            "v_key": v_key,
            "guid": guid,
            "number": self.get_number(v_key, guid, '1', '12'),
            "crawlerCookie": crawlerCookie
        }

        data = json.dumps(data, separators=(',', ':'))
        res = self.request(session, 'POST', url, headers=self.headers, data=data, name=Law.code_dic[xzqh], tojson=True)
        return res['list']

    def crawl_detail_12348(self, session: Session, ddd):
        """
        http://www.12348.gov.cn/lawerdeptlist/getlawerdeptlist
        """
        data = {"lsswsbs": ddd.get("lsswsbs")}
        data = json.dumps(data, separators=(',', ':'))
        url = "http://www.12348.gov.cn/lawdeptinfo/getlawdeptinfo"
        res = self.request(session, 'POST', url, data=data, tojson=True, name='getlawdeptinfo', headers=self.headers)
        logger.info(f'{ddd.get("lsswsbs")} --> {res}')
        return res

    def crawl_lawyers(self, session: Session, ddd):
        """
        http://www.12348.gov.cn/lawerdeptlist/getlawerdeptlist
        """
        data = {"pkid": ddd.get("lsswsbs"), "pageNum": 1, "pageSize": ddd.get('nums', 10000)}
        url = "http://www.12348.gov.cn/lawdeptinfo/getlawerlist"
        res: dict = self.request(session, 'POST', url, json=data, tojson=True)
        logger.info(f"{ddd['lsswsmc']} --> {res}")
        return res

    def parse_page(self, detail_data):
        law_firm_info = {
            'lawFirmName': detail_data.get('lsswsmc'),
            'creditCode': detail_data.get('tyshxydm'),
            'dateOfEstablishment': detail_data.get('pzslsj'),
            'telephone': detail_data.get('zsdh'),
            'permit': detail_data.get('zyzh'),  # 执业证号
            'address': detail_data.get('zsd'),  # 机构地址'' (4658407024)
            'summary': re.sub(r'<.*?>', '', detail_data.get('jj')),  # 简介
            'address_remarks': detail_data.get('district'),  # 所在区县
        }
        self.remove_None(law_firm_info)
        return law_firm_info

    def parse_lawyers(self, detail_data):
        if not detail_data:
            return [], []
        lawyer_list = []
        lawyers = detail_data.get('list')
        for lawyer in lawyers:
            img_url = ""
            if lawyer.get('XZQH') and lawyer.get('PIC'):
                # 提取 XZQH 的前两位并加上 '00'
                xzqh_part = lawyer['XZQH'][:2] + '00'
                # 提取 PIC 的文件名部分（去掉扩展名）
                pic_name = lawyer['PIC'][:lawyer['PIC'].rfind('.')]
                # 提取 PIC 的扩展名部分
                pic_extension = lawyer['PIC'][lawyer['PIC'].rfind('.') + 1:]
                # 构造 URL https://www.12348.gov.cn/imagetype/
                img_url = f"{xzqh_part}/lsfw/lsuser/{pic_name}/{pic_extension}"
            lawyer_info = {
                'name': lawyer.get('lsswsmc'),
                'human_name': lawyer.get('xm'),
                'License': lawyer.get('zyzh').strip(),
                'pic_url': img_url,
                'work_year': str(lawyer['years']) if lawyer.get('years') else None,
                'source': '12348'
            }
            self.remove_None(lawyer_info)
            lawyer_list.append(lawyer_info)
        lawyer_list2 = copy.deepcopy(lawyer_list)
        for i in lawyer_list2:
            i['name'] = i['human_name']
            del i['human_name']
            i['license'] = i['License']
            del i['License']
        return lawyer_list, lawyer_list2

    @staticmethod
    def get_number(v_key, guid, pageNum, pageSize):
        string = md5((v_key + guid + str(pageNum) + "." + pageSize).encode()).hexdigest()
        number = ord(string[10]) + ord(string[3]) + ord(string[1]) + ord(string[6]) + ord(string[8]) + ord(string[5])
        return number


class Acla(CrawlerTools):
    def __init__(self):
        super().__init__()
        self.url = 'https://credit.acla.org.cn/credit/lawFirm?picCaptchaVerification=true&keyWords='
        self.url_lawyer = 'https://credit.acla.org.cn/credit/lawyer?picCaptchaVerification=true&keyWords='
        self.headers = {
            "Referer": "https://credit.acla.org.cn/credit/lawFirm?keyWords=313100003326104621&lawFirmType=none&lawyerNumber=none&partnerNumber=none&orgType=none&zone=none&_hasPunish=on&_hasHonor=on&picCaptchaVerification=&pageIndex=1&pageSize=10&sorter=",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        }
        self.font_list = ['王', '李', '张', '师', '律', '事', '所', '务', '刘', '陈', '杨', '文', '东', '华', '晓', '海', '林', '明', '黄', '赵', '周',
                          '吴',
                          '伟', '江', '建', '丽', '军', '志', '徐', '孙', '国', '金', '平', '北', '红', '朱', '广', '云', '龙', '杰', '马', '玉', '高',
                          '春',
                          '胡', '郭', '小', '强', '宇', '新', '艳', '辉', '永', '京', '峰', '敏', '南', '鹏', '山', '何', '苏', '郑', '涛', '燕', '俊',
                          '婷',
                          '宁', '成', '勇', '玲', '飞', '天', '芳', '静', '梅', '佳', '慧', '波', '罗', '娟', '阳', '斌', '庆', '德', '雪', '超', '荣',
                          '梁',
                          '安', '君', '霞', '亚', '西', '丹', '英', '洪', '宋', '谢', '光', '许', '刚', '韩', '立', '唐', '方', '萍', '河', '程', '宏',
                          '亮',
                          '田', '曹', '冯', '福', '利', '瑞', '肖', '青', '川', '振', '娜', '清', '忠', '叶', '兴', '浩', '邓', '正', '董', '民', '生',
                          '磊',
                          '武', '于', '曾', '丁', '彭', '旭', '潘', '莉', '学', '秀', '颖', '袁', '祥', '思', '杜', '凯', '沈', '蒋', '琳', '鑫', '锋',
                          '上',
                          '兰', '中', '泽', '蔡', '余', '洁', '贵', '魏', '健', '松', '博', '夏', '任', '欣', '凤', '良', '吕', '卫', '雷', '智', '胜',
                          '湖',
                          '一', '世', '子', '万', '嘉', '元', '晶', '崔', '姚', '宝', '倩', '梦', '莹', '星', '大', '姜', '四', '洋', '康', '石', '卢',
                          '雨',
                          '彬', '汪', '市', '钟', '秋', '范', '孟', '兵', '美', '晨', '雅', '州', '陆', '吉', '剑', '家', '远', '维', '坤', '贾', '长',
                          '谭',
                          '向', '琴', '媛', '浙', '楠', '达', '白', '力', '秦', '月', '昌', '彦', '毅', '付', '桂', '恒', '爱', '和', '义', '冰', '惠',
                          '翔',
                          '冬', '群', '侯', '廖', '邹', '全', '薛', '盛', '黎', '琪', '章', '少', '雯', '乐', '继', '尹', '熊', '珍', '蒙', '邱', '源',
                          '景',
                          '威', '淑', '贺', '朝', '凌', '璐', '怡', '雄', '辽', '琦', '锦', '鸿', '仁', '进', '常', '戴', '古', '树', '铭', '段', '顾',
                          '津',
                          '宗', '珊', '培', '琼', '闫', '启', '顺', '钱', '凡', '信', '哲', '富', '重', '帅', '友', '卓', '耀', '科', '克', '银', '史',
                          '陶',
                          '奇', '毛', '豪', '双', '诚', '蓉', '韦', '贤', '邵', '尚', '齐', '昊', '柳', '郝', '森', '法', '严', '然', '瑶', '徽', '丰',
                          '连',
                          '欢', '栋', '传', '蕾', '芬', '权', '会', '尔', '迪', '岩', '岳', '睿', '婧', '苗', '书', '保', '泉', '开', '才', '龚', '炜',
                          '扬',
                          '路', '茂', '先', '申', '黑', '政', '帆', '聪', '施', '晖', '虹', '鲁', '航', '孔', '菲', '乔', '汉', '悦', '发', '道', '甘',
                          '汤',
                          '欧', '娇', '虎', '葛', '瑜', '合', '易', '喜', '敬', '艺', '花', '舒', '兆', '温', '牛', '露', '锐', '樊', '延', '润', '阿',
                          '泰',
                          '跃', '彩', '翠', '薇', '玮', '诗', '邢', '来', '纪', '勤', '香', '萌', '如', '颜', '靖', '绍', '业', '代', '恩', '茜', '内',
                          '茹',
                          '妮', '园', '陕', '艾', '莲', '素', '妍', '庄', '钰', '木', '疆', '轩', '运', '益', '圣', '莎', '同', '可', '辰', '希', '俞',
                          '倪',
                          '鸣', '殷', '赖', '涵', '巍', '伦', '昕', '通', '提', '越', '升', '根', '佩', '傅', '治', '城', '灵', '晋', '翟', '承', '男',
                          '霖',
                          '莫', '水', '冠', '士', '聂', '彤', '心', '楚', '依', '应', '有', '坚', '伍', '增', '巧', '洲', '腾', '斯', '冉', '震', '童',
                          '仲',
                          '晴', '礼', '真', '若', '关', '耿', '旺', '曦', '菊', '邦', '其', '善', '怀', '潇', '为', '都', '之', '柏', '日', '覃', '柯',
                          '时',
                          '衡', '占', '焦', '曼', '鼎', '朋', '璇', '庞', '曲', '湘', '奎', '炳', '婉', '左', '祝', '彪', '理', '原', '盈', '祖', '季',
                          '铁',
                          '肃', '三', '奕', '沙', '骏', '逸', '风', '联', '捷', '芝', '包', '灿', '馨', '廷', '尧', '钦', '乾', '仕', '煜', '珠', '守',
                          '行',
                          '焕', '格', '爽', '崇', '羽', '毕', '卿', '蕊', '韬', '竹', '孝', '加', '婕', '芸', '皓', '言', '笑', '滨', '昆', '昭', '雁',
                          '谷',
                          '本', '梓', '显', '碧', '宪', '纯', '定', '芹', '自', '单', '菁', '珂', '仪', '辛', '盼', '蓝', '满', '瑾', '登', '迎', '硕',
                          '宜',
                          '征', '裴', '钢', '甜', '姣', '太', '育', '霍', '圆', '容', '蔚', '紫', '柱', '姗', '昱', '众', '麦', '畅', '存', '游', '展',
                          '伊',
                          '烨', '钧', '商', '禹', '普', '勋', '熙', '舟', '相', '臣', '琛', '弘', '晟', '桥', '修', '隆', '詹', '谦', '玥', '人', '司',
                          '拉',
                          '喻', '柴', '岚', '标', '令', '管', '宾', '靳', '阮', '微', '赛', '涂', '杭', '米', '贞', '均', '鲍', '能', '深', '劲', '韵',
                          '鹤',
                          '前', '厚', '淼', '驰', '钊', '锡', '百', '照', '汇', '公', '娅', '沛', '翁', '蓓', '晗', '枫', '麟', '知', '臻', '环', '骆',
                          '裕',
                          '瀚', '瑛', '济', '滕', '年', '诺', '渊', '轶', '创', '闻', '斐', '庭', '祁', '解', '贝', '亭', '品', '基', '尤', '典', '寒',
                          '姝',
                          '宽', '久', '买', '饶', '伯', '功', '堂', '意', '房', '印', '祺', '丛', '符', '名', '儒', '娴', '炎', '影', '党', '穆', '霄',
                          '岭',
                          '项', '献', '喆', '九', '干', '卜', '蒲', '实', '攀', '牟', '懿', '汝', '猛', '经', '珺', '战', '望', '晔', '镇', '郁', '娥',
                          '筱',
                          '桐', '冲', '绪', '作', '融', '苑', '映', '泓', '举', '池', '热', '晏', '乃', '榕', '亦', '宣', '果', '窦', '仙', '甫', '非',
                          '寿',
                          '翰', '声', '宫', '侠', '瀛', '布', '缪', '冀', '初', '里', '卉', '图', '营', '啸', '娄', '屈', '费', '寅', '曙', '焱', '旗',
                          '楼',
                          '姬', '官', '多', '褚', '铮', '夫', '孜', '泳', '岑', '峻', '虞', '潮', '边', '秉', '桑', '玺', '哈', '化', '祎', '婵', '策',
                          '特',
                          '巴', '戈', '财', '车', '禄', '峥', '致', '领', '恺', '简', '从', '野', '地', '壮', '毓', '起', '咏', '戚', '渝', '媚', '朗',
                          '竞',
                          '魁', '仇', '琨', '翼', '拓', '沁', '农', '召', '闵', '席', '以', '妹', '郎', '观', '珏', '圳', '臧', '舜', '溪', '佟', '迟',
                          '誉',
                          '念', '旻', '幸', '栾', '邬', '允', '卞', '挺', '区', '乌', '瞿', '纬', '冷', '浪', '赫', '桦', '琰', '妙', '桃', '音', '娣',
                          '吾',
                          '唯', '聚', '佑', '再', '澜', '阎', '宸', '尼', '予', '滢', '煌', '芮', '添', '革', '骁', '好', '杉', '岗', '赟', '萱', '昂',
                          '港',
                          '伶', '鹰', '庚', '衍', '歌', '俐', '隋', '朴', '刁', '巨', '得', '苟', '荆', '井', '甲', '封', '枝', '璟', '绮', '必', '殿',
                          '匡',
                          '语', '炯', '昀', '二', '颂', '优', '寇', '苹', '尊', '查', '临', '现', '含', '麒', '奥', '繁', '努', '侧', '干', '口', '佐',
                          '遥',
                          '璞', '垚', '端', '敖', '杏', '鞠', '谈', '豫', '赞', '雍', '禾', '廉', '旋', '桢', '储', '霜', '首', '茵', '栗', '习', '韶',
                          '漫',
                          '靓', '楷', '滔', '奚', '居', '纳', '浦', '甄', '在', '敦', '淳', '丘', '争', '鉴', '留', '五', '吐', '阁', '巫', '见', '闯',
                          '慕',
                          '巩', '至', '键', '盟', '佘', '邝', '那', '度', '迅', '祯', '胥', '旦', '灏', '门', '芦', '效', '淮', '熠', '球', '纲', '步',
                          '训',
                          '隽', '嵩', '放', '旸', '鸽', '萧', '淇', '澄', '俭', '烈', '玄', '妤', '慈', '选', '不', '湛', '班', '麻', '分', '铃', '镜',
                          '屹',
                          '恬', '蕴', '蜀', '索', '洛', '火', '闽', '芷', '玛', '嵘', '墨', '骥', '库', '谋', '侃', '塔', '筠', '计', '述', '台', '呼',
                          '霏',
                          '奉', '鹿', '罡', '稳', '笛', '鲜', '丙', '玫', '堃', '帮', '黔', '蔺', '棋', '涌', '烽', '研', '拥', '支', '沐', '劳', '卡',
                          '社',
                          '岛', '练', '湛', '穗', '粤']
        with open(f'{parent_path}/scripts/refer__1711.js', 'r', encoding='utf-8') as f:
            self.js_compile_1711 = execjs.compile(f.read())
        with open(f'{parent_path}/scripts/key.js', 'r', encoding='utf-8') as f:
            self.js_code_key = f.read()
        with open(f'{parent_path}/scripts/crypto.js', 'r', encoding='utf-8') as f:
            self.js_compile_crypto = execjs.compile(f.read())

    def crawl_list(self, code) -> list:
        url = "https://credit.acla.org.cn/api/credit/MapDataService/list"
        data = {"pageSize": 4000, "pageIndex": 1, "code": code}
        res = self.request(self.get_new_session(), 'POST', url, json=data, tojson=True, name='list')
        return res['items']

    def get_person(self, html):
        html = self.replace_font(html)
        soup = BeautifulSoup(html, 'lxml')
        list_ = soup.select('.search-result-list div.search-item-desc-other div.search-desc-introduce span')
        if not list_:
            return ''
        name = list(list_[0].stripped_strings)
        name = ''.join(name)
        return name

    def crawl_detail(self, name):
        url = self.url + quote(name)
        html = self.add_cookies_params_request(url)
        soup = BeautifulSoup(html, 'lxml')
        a = soup.select_one('.search-result-list a')
        if not a:
            raise MyException('搜索为空')
        url_encrypted = re.search("\('(.*?)',", a.get('onclick')).group(1)
        url = self.decrypt_url(html, url_encrypted, 'lawFirm')
        name = self.get_person(html)
        html = self.add_cookies_params_request(url)
        return html, name

    def add_cookies_params_request(self, url) -> str:
        url = self.create_url(url)
        for i in range(10):
            session = self.get_new_session()
            html = self.request(session, 'GET', url, name='add_cookies_params-1')
            if '请进行验证' in html:
                logger.warning(f'触发阿里滑块风控-{i + 1}')
                continue
            self.update_cookie(session, html)
            html = self.request(session, 'GET', url, name='add_cookies_params-2')
            if '验证码' in html:
                logger.warning(f'cookie失效-{i + 1}')
                continue
            time.sleep(0.3)
            return html
        raise Exception('10次触发阿里滑块风控')

    def create_url(self, url):
        parsed_url = urlparse(url)
        url = self.js_compile_1711.call('get_url', url, parsed_url.path, '?' + parsed_url.query if parsed_url.query else '')
        logger.info(url)
        return url

    def update_cookie(self, session, html):
        I = 'var I = [' + re.search(r'=\[(.*?)];\(function\(', html).group(1) + '];\n'
        js_compile = execjs.compile(I + self.js_code_key)
        key = js_compile.call('get_key')
        UM_UNDEFINED = self.js_compile_crypto.call('DES_Encrypt', self.generate_random_string(), key)
        session.cookies.set('UM_UNDEFINED', UM_UNDEFINED)

    def decrypt_url(self, html, url, url_type):
        I = 'var I = [' + re.search(r'=\[(.*?)];\(function\(', html).group(1) + '];\n'
        js_compile = execjs.compile(I + self.js_code_key)
        key = js_compile.call('get_key')
        url = f'https://credit.acla.org.cn/credit/{url_type}/' + self.js_compile_crypto.call('DES_Decrypt', url, key)
        # logger.info(url)
        return url

    def replace_font(self, html):
        for _ in range(3):
            url_ttf = ''
            try:
                url_ttf = re.search(r'https://static.homolo.net/credit/prototype/static/font/(.*?)/font.ttf', html)
                if not url_ttf:
                    raise Exception('字体url为空')
                url_ttf = url_ttf.group(1)
                ttf_path = f'{parent_path}/scripts/cache_font/{url_ttf}.ttf'
                if os.path.exists(ttf_path):
                    with open(ttf_path, 'rb') as f:
                        map_dict = self.map_font(f.read())
                else:
                    url = f'https://static.homolo.net/credit/prototype/static/font/{url_ttf}/font.ttf'
                    response = self.request(requests.session(), 'GET', url, toRaw=True, name=f'font-{url}', long_proxy=True, long_timeout=60)
                    self.save_font(response.content, url_ttf.split('/')[-2])
                    map_dict = self.map_font(response.content)
                for key, value in map_dict.items():
                    html = html.replace(key, str(value))
                return html
            except Exception as e:
                logger.error(f'error: {e} {_ + 1} {url_ttf}')
        raise Exception('字体替换失败')

    def parse(self, html, selector, type_='lawFirm'):
        html = self.replace_font(html)

        soup = BeautifulSoup(html, 'lxml')
        lis = soup.select(selector)
        info = {}
        for li in lis:
            list_: list[str] = list(li.stripped_strings)
            info[list_[0].replace('\u2003', '')] = ''.join(list_[1:])
        name = ''.join(list(soup.select_one('div.person-name-inner.pull-left').stripped_strings))
        code = soup.select_one('div.person-desc-inner.pull-left').text
        info['name'] = name.replace('干', '千').replace('则', '侧').replace('湛', '谌')
        info['code'] = code

        if type_ == 'lawyer':
            image_url = re.search(r"avatar-photo/(.*?)\?thumb=150x200", html)
            if image_url:
                pic_url = 'https://credit.acla.org.cn/avatar-photo/' + image_url.group(1) + '?thumb=150x200'
                info['pic_url'] = pic_url

        legalPerson = ''
        if type_ == 'lawFirm':
            # spans = soup.select('div.card-content-main li.common-name-item.pull-left span.inner-name')
            spans = soup.select('div.card-content-main>div:nth-child(1) li.common-name-item.pull-left span.inner-name')
            if spans:
                legalPerson = ''.join(spans[0].stripped_strings)
        logger.info(f'{name}-{code}-{info}')
        return info, legalPerson

    @staticmethod
    def parse_map_law(data, legalPerson):
        if not data:
            return
        law_firm_info = {
            'lawFirmName': data.get('name'),
            'legalPerson': legalPerson,
            # 'email': data.get('email'),
            'postCode': data.get('邮政编码：'),
            'creditCode': data.get('code'),
            # 'permit': data.get('zyzh'),  # 执业证号
            'registeredCapital': data.get('设立资产：'),
            'dateOfEstablishment': data.get('准予设立日期：').replace('年', '-').replace('月', '-').replace('日', ''),
            'telephone': data.get('联系电话：'),
            'fax': data.get('传 真：'),
            'address': data.get('本所住所：'),
            # 'address_remarks': data.get('qhmc'),
            'organizationForm': data.get('组织形式：'),
            # 'practiceState': '正常',
            # 'websit': data.get('mainUrl'),
            'licensingOrganizations': data.get('发证机关：'),
            'authorities': data.get('主管机关：'),
        }
        CrawlerTools.remove_None(law_firm_info)
        logger.info(f'{data["name"]} - {data["code"]}  --> {law_firm_info}')
        return law_firm_info

    @staticmethod
    def generate_random_string(length=32):
        return ''.join(random.choices(string.hexdigits.lower(), k=length))

    @staticmethod
    def save_font(content: bytes, name):
        with open(f'{parent_path}/scripts/cache_font/{name}.ttf', 'wb') as f:
            f.write(content)

    def map_font(self, content: bytes):
        font = TTFont(BytesIO(content))
        code_list = font.getGlyphOrder()[3:]
        code_list = [i.replace('uni', '&#x') + ';' for i in code_list]
        if len(code_list) == 1083:
            return dict(zip(code_list, self.font_list))
        if len(code_list) == 1093:
            number = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
            number.extend(self.font_list)
            return dict(zip(code_list, number))


if __name__ == '__main__':
    pass
    law_ = Law()
    law_info_ = {
        'name': '山东谌润律师事务所',
        'code': '31370000349318783F'
    }
    session_ = law_.get_new_session()
    print(law_.crawl_first(session_, law_info_['name'], xzqh=law_info_["code"][2:4]))
