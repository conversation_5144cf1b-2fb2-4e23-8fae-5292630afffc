import json
import base64
import re
import time
from PIL import Image, ImageSequence
import ddddocr
import requests
from requests import Session, Response
from threading import current_thread, Lock
from typing import Dict, Union

from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import <PERSON><PERSON><PERSON>, CrawlerTask, MyException, CrawlerTools
from resx.func import cur_ts_sec
from resx.redis_types import RedisQueue
from resx.log import setup_logger
from resx.mysql_dao import MySQLDao
from resx.config import *

logger = setup_logger(name=__name__)


class CrawlerHKTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)

        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class CrawlerHK(Crawler):
    @classmethod
    def get_name(cls):
        return 'hk'

    def __init__(self, **kwargs):
        self.sessions: Dict[int, Session] = dict()
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        }
        self.timeout = 10
        self.lock = Lock()
        self.dao_image = MySQLDao(db_tb_name='prism.hk_image_list', **CFG_MYSQL_GS_OUTER)
        self.dao_hk = MySQLDao(db_tb_name='prism.company_hk', **CFG_MYSQL_GS_OUTER)
        super().__init__(input_queue=RedisQueue(name='hk', **CFG_REDIS_GS, db=9), task_cls=CrawlerHKTask, eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: CrawlerHKTask = self.get_crawler_task()
        eventlog = task.eventlog
        br_no = task.keyword
        try:
            for n in range(5):
                if self.keep_alive(br_no):
                    break
                if self.Log_in():
                    if not self.keep_alive(br_no, num=2):
                        continue
                    break
                elif n > 3:
                    logger.error(f'br_no: [{br_no}] - 登录失败')
                    raise MyException('登录失败')

            if not re.fullmatch(r'[A-Z0-9]{8}', br_no):
                search = self.search(br_no)
                if not search:
                    raise MyException('搜索为空')
                br_no = search[0]['brno']

            company_num = self.get_company_num(br_no)
            # 照面信息
            base_info = self.ps_company_name_base_information_search_brno(br_no)
            if not base_info:
                raise MyException('搜索为空')
            # 曾用名信息
            res_his = self.ps_company_name_history_search_history_data(br_no)
            # 影像
            doc_list, image_types = self.ps_doc_list(br_no)
            # doc_list, image_types = [], {}

            base_info['company_num'] = company_num
            task.pages['doc_list.json'] = json.dumps(doc_list, ensure_ascii=False)
            task.pages['image_types.json'] = json.dumps(image_types, ensure_ascii=False)
            task.pages['res.json'] = json.dumps(base_info, ensure_ascii=False)
            task.pages['res_his.json'] = json.dumps(res_his, ensure_ascii=False)
            task.pages['address1.json'] = json.dumps(self.get_address1(br_no), ensure_ascii=False)
            task.pages['address2.json'] = json.dumps(self.get_address2(br_no), ensure_ascii=False)
        except MyException as e:
            if '失败' in e.message:
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
            if e.message == '放弃':
                eventlog.code = StatusCode.GIVE_UP
            if e.message == '搜索为空':
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
        except Exception as e:
            logger.error(f'br_no: [{br_no}] - {CrawlerTools.custom_traceback(e)}')
            eventlog.code = StatusCode.GIVE_UP
            raise e

    def search(self, keyword):
        companyNameCN, companyNameEN = '', ''
        if re.search(r'[A-Za-z]+', keyword):
            companyNameEN = keyword
            actvyCode = "S-ENXN"
            companyLang = 0
        else:
            companyNameCN = keyword
            actvyCode = "S-CNXN"
            companyLang = 1

        url = "https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name/search-name.do"
        data = {
            "searchMode": 0, "activeStatus": "L", "ofc": False, "total": 1, "pageSize": 50,
            "defaultCurrent": 1, "current": 1, "currentNameOnly": "Y", "displayTotal": 1,
            "companyLang": companyLang,
            "companyNameEN": companyNameEN,
            "companyNameCN": companyNameCN,
            "actvyCode": actvyCode,
        }
        res: dict = self.request("POST", url, json=data, tojson=True, name='search-name.do')
        return res['data']

    def get_company_num(self, br_no):
        url = f'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name/mapping/br/{br_no}.do'
        res: dict = self.request("GET", url, tojson=True, name='mapping')
        if res.get('data'):
            return res['data'].get('zufgjNo', '')
        return ''

    def keep_alive(self, br_no, num=1):
        url = 'https://www.e-services.cr.gov.hk/ICRIS3EP/user/menu/list.do'
        res = self.request("GET", url, tojson=True, name='保活')
        if res.get('successful'):
            logger.info(f'br_no: [{br_no}] - 保活成功:{num}')
            return True
        else:
            logger.warning('br_no: [{}] - 保活失败'.format(br_no))
            return False

    def Log_in(self):
        self.system_login_unregistered_or_userPassword()
        for _ in range(3):
            captcha: dict = self.system_common_captcha()
            code = self.Base64Gif_to_code(captcha)
            success = self.ps_public_search_access_statement_accept(code, captcha)
            if success:
                return True

    def ps_doc_list(self, br_no):
        url = "https://www.e-services.cr.gov.hk/ICRIS3ES/ps/document-index/count.do"
        data = {"actvyCode": "S-DISAA", "brNo": br_no, "coyTyp": "I", "entryActvyCode": "S-DISAA-EN", "group": "ALL",
                "listSearchActvyCode": "S-DISAA-P1", "pageIndex": 1, "pageSize": 50, "yearSearchType": 0}
        res = self.request("POST", url, json=data, tojson=True, name='count.do')
        if not res.get('data'):
            return []
        count = res['data']
        count_ = self.get_doc_counts(br_no)
        if count_ >= count:
            return [], {}

        image = {
            '01': '周年申報表及帳目 / 財務報表',
            '02': '董事、公司秘書及授權代表',
            '03': '成立/註冊及更改名稱',
            '04': '按揭及押記',
            '05': '接管、清盤、撤銷註冊、剔除公司名稱行動、不活動狀態及法院命令',
            '06': '註冊辦事處、登記冊備存地點及註冊非香港公司地址',
            '07': '股本結構',
            '08': '其他',
            'ALL': 'ALL'
        }
        image_list = []
        image_types = {}
        for k, v in image.items():
            url = "https://www.e-services.cr.gov.hk/ICRIS3ES/ps/document-index/list-search.do"
            data = {"group": k, "yearSearchType": 0, "brNo": br_no, "pageSize": count, "pageIndex": 1}
            res = self.request("POST", url, json=data, tojson=True, name=f'list-search.do-{v}', isDetail=True)
            if k == 'ALL':
                image_list_ = res['data']
                break
            if res['data']:
                for i in res['data']:
                    i: dict
                    image_types[i['docNo']] = image_types.get(i['docNo'], '') + v + ' , '
                    image_list.append({
                        'docID': i['docID'],
                        'docChNameDisplay': i['docChNameDisplay'],
                        'filingDate': i['filingDate'],
                        'noOfPg': i['noOfPg'],
                        'fileSize': i['fileSize'],
                        'statusForDisplay': i['statusForDisplay'],
                        'image_type': image_types[i['docNo']]
                    })

        diff = list(set([i['docID'] for i in image_list_]) - set([i['docID'] for i in image_list]))
        for i in image_list_:
            if i['docID'] in diff:
                image_types[i['docID']] = '其他'
                image_list.append(i)

        image_list = CrawlerTools.unique(image_list, 'image_type')
        return image_list, image_types

    def get_doc_counts(self, br_no) -> int:
        hk = self.dao_hk.get(br_num=br_no)
        if not hk:
            return 0
        a = list(self.dao_image.select_many(f'select image_id_str,image_type from prism.hk_image_list where hk_id = {hk["id"]}'))
        for i in a:
            if i['image_type'] == '':
                return 0
        a = CrawlerTools.unique(a, 'image_type')
        return len(list(a))

    def ps_public_search_access_statement_accept(self, code, captcha):
        url = "https://www.e-services.cr.gov.hk/ICRIS3EP/ps/public-search/access/statement_accept.do"
        data_free = {"acceptList": [1], "captchaId": captcha['data']['captchaId'], "captchaImg": captcha['data']['captchaImg'],
                     "captchaCode": code, "loginType": "iguest", "chiName": "天眼查", "engSname": "TYC", "engOname": "TYC",
                     "docType": "3", "idDocNum": "1", "issPlace": "CHN", "issAuth": "1"}
        res = self.request("POST", url, json=data_free, tojson=True, name='statement_accept.do')
        if res.get('successful'):
            return True

    def system_common_captcha(self):
        url = "https://www.e-services.cr.gov.hk/ICRIS3EP/system/common/captcha.do"
        res = self.request("GET", url, tojson=True, name='captcha.do')
        return res

    def system_login_unregistered_or_userPassword(self):
        json_data_free = {"loginType": "iguest"}
        url_free = "https://www.e-services.cr.gov.hk/ICRIS3EP/system/login/unregistered.do"
        self.request("POST", url_free, json=json_data_free, name='unregistered.do')

    def ps_company_name_base_information_search_brno(self, brNo):
        url = f'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name/base-information/search-brno.do?brNo={str(brNo)}'
        res: dict = self.request("GET", url, tojson=True, name=f'{brNo}-照面信息爬取完成', isDetail=True)
        return res['data']['baseData']

    def ps_company_name_history_search_history_data(self, brNo):
        url = f'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name-history/search-history-data.do?brNo={brNo}'
        res = self.request("GET", url, tojson=True, name=f'{brNo}-曾用名信息爬取完成')
        return res['data']

    def get_address1(self, br_num):
        url = f'https://data.cr.gov.hk/cr/api/api/v1/api_builder/json/local/search?query[0][key1]=Brn&query[0][key2]=equal&query[0][key3]={br_num}'
        try:
            res = self.request('GET', url, tojson=True, name='hk_address')
            return res
        except MyException as e:
            return {}

    def get_address2(self, br_num):
        url = f'https://data.cr.gov.hk/cr/api/api/v1/api_builder/json/local/search?query[0][key1]=Brn&query[0][key2]=equal&query[0][key3]={br_num}'
        try:
            res = self.request('GET', url, tojson=True, name='hk_address')
            return res
        except MyException as e:
            return {}

    @staticmethod
    def Base64Gif_to_code(base64_string):
        try:
            def overlay_images(image1, image2):
                # 调整图像的透明度
                image1 = adjust_opacity(image1, 0.7)
                image2 = adjust_opacity(image2, 0.7)
                # 对齐两张图片
                image_size = (max(image1.width, image2.width), max(image1.height, image2.height))
                image1 = image1.resize(image_size)
                image2 = image2.resize(image_size)
                # 创建一个空白的合并图像
                merged_image = Image.new("RGBA", image_size)
                # 将两张图片叠加在一起
                merged_image = Image.alpha_composite(merged_image, image1)
                merged_image = Image.alpha_composite(merged_image, image2)
                return merged_image

            def adjust_opacity(image, opacity):
                # 获取图像的Alpha通道
                alpha = image.split()[3]
                # 调整透明度
                alpha = alpha.point(lambda p: p * opacity)
                # 合并调整后的Alpha通道到原图像中
                image.putalpha(alpha)
                return image

            # 去除Base64字符串中的数据类型前缀
            base64_string = base64_string['data']['captchaImg'].split('base64,')[-1]
            # 将Base64字符串解码为字节数组
            gif_data = base64.b64decode(base64_string)
            # 将字节数组转换为字节流
            gif_stream = io.BytesIO(gif_data)

            image = Image.open(gif_stream)
            frame_num = 0
            ocr = ddddocr.DdddOcr(show_ad=False)
            result = Image.new('RGBA', image.size)
            for frame in ImageSequence.Iterator(image):
                new_image = Image.new("RGBA", image.size)
                new_image.paste(frame)
                result = overlay_images(result, new_image)
                frame_num += 1
            res = ocr.classification(result)
            return res
        except Exception as e:
            logger.error(e)
            return e

    def request(self, method: str, url: str, params: dict = None, data: Union[dict, str] = None, json: dict = None, path: str = None,
                name: str = '', tojson=False, toRaw=False, long_proxy=False, long_timeout=30, isDetail=False) -> Union[dict, str, Response]:
        with self.lock:
            tid = current_thread().ident
            if tid not in self.sessions:
                self.sessions[tid] = Session()
            session = self.sessions[tid]

        for i in range(10):
            response = None
            try:
                a = time.time()
                request_params = {'method': method, 'url': url, 'data': data, 'headers': self.headers,
                                  'verify': False, 'timeout': self.timeout, 'params': params, 'json': json,
                                  'proxies': {'http': 'http://************:30636', 'https': 'http://************:30636'}}
                if long_proxy:
                    # request_params['proxies'] = self.get_long_proxy()
                    request_params['timeout'] = long_timeout
                response = session.request(**request_params)

                if response.status_code == 400 and name == 'hk_address':
                    return {}
                if response.status_code != 200 and name != '保活':
                    logger.warning(f'{name}-{i + 1} --> {response.status_code}')
                    del session.cookies['proxyBase']
                    continue
                logger.info(f'{name} --> {response.status_code} --> time: {time.time() - a}')
                time.sleep(0.5)

                if toRaw:
                    return response
                if isDetail:
                    a = re.sub(r'[\n\r\t]', '', response.text)
                    logger.info(f'{name} --> {a}')
                if tojson:
                    json_ = response.json()
                    if isinstance(json_, dict) and isinstance(json_.get('data'), dict) and 'highVolumeBlocked' in json_['data']:
                        time.sleep(3)
                        continue
                    return json_
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'{name} --> continue{i} exception: {e} ip: {CrawlerTools.get_ip(session)}')
                del session.cookies['proxyBase']
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'{name} --> continue{i} 状态码：{status} res: {text} exception: {CrawlerTools.custom_traceback(e)}')
                del session.cookies['proxyBase']
        raise MyException('接口连续失败')
