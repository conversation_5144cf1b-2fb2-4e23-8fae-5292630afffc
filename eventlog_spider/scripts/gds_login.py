import requests
import ddddocr
from loguru import logger
import re
import base64
import uuid
import hashlib
from urllib.parse import urlenco<PERSON>
from datetime import datetime, timedelta
import pytz
import copy
import time
from urllib.parse import quote
from requests.sessions import Session
import re
import cn2an
from requests.adapters import HTTPAdapter
from eventlog_spider.crawler.crawler import CrawlerTools

headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
timeout = 3
ocr = ddddocr.DdddOcr(show_ad=False)


def hex_to_base64url(hex_str):
    bytes_data = bytes.fromhex(hex_str)
    base64_str = base64.b64encode(bytes_data).decode('utf-8')
    base64_url_str = base64_str.replace('+', '-').replace('/', '_').rstrip('=')
    return base64_url_str


def get_uuid():
    return str(uuid.uuid4())


def sha256_digest(input_str):
    hash_object = hashlib.sha256(input_str.encode())
    return hash_object.hexdigest()


def get_now_date(expires_in):
    dt = datetime.now()
    dt1 = dt + timedelta(seconds=expires_in)
    dt2 = dt + timedelta(minutes=20)
    china_tz = pytz.timezone('Asia/Shanghai')
    dt1 = china_tz.localize(dt1)
    dt2 = china_tz.localize(dt2)
    date_str1 = dt1.strftime('%a %b %d %Y %H:%M:%S GMT%z (%Z)')
    date_str2 = dt2.strftime('%a %b %d %Y %H:%M:%S GMT%z (%Z)')

    return date_str1.replace('(CST)', '(中国标准时间)'), date_str2.replace('(CST)', '(中国标准时间)')


def index(tokens, user_info):
    url = "https://www.gds.org.cn/"
    res = requests.get(url, headers=headers, verify=False, cookies={
        "accessToken": tokens['accessToken'],
        "accessToken_v4": tokens['accessToken'],
        'expires_in_v4': 7200,
        "firmInfo_v4": '{%22FirmId%22:-100%2C%22FirmName%22:null%2C%22Address%22:null%2C%22BranchCode%22:-100%2C%22BranchName%22:null%2C%22IsLogout%22:0%2C'
                       '%22IsSystemMember%22:false%2C%22IsLateForLicense%22:false%2C%22GLN%22:null}',
        "refreshToken_v4": tokens['refresh_token'],
        'refreshtime_v4': '',
        'tokenExpire_v4': '',
        'userInfo': user_info.encode('utf-8').decode('latin-1'),
        'userInfo_v4': quote(user_info),
        'userRole_v4': 'Mine'
    })
    logger.info(f'index: {res.status_code}')


def authorize(session, state, code_challenge):
    url = "https://passport.gds.org.cn/connect/authorize"
    params = {
        "client_id": "vuejs_code_client",
        "redirect_uri": "https://www.gds.org.cn/#/callback",
        "response_type": "code",
        "scope": "openid profile api1 offline_access",
        "state": state,
        "code_challenge": code_challenge,
        "code_challenge_method": "S256",
        "response_mode": "query"
    }
    res = crawler_tools.request(session, 'GET', url, headers=headers, params=params, timeout=timeout, name='authorize')
    __RequestVerificationToken = re.search(r"__RequestVerificationToken.*?value=\"(.*?)\"", res).group(1)
    # logger.info(f"__RequestVerificationToken: {__RequestVerificationToken}")

    return __RequestVerificationToken


def get_pic(session):
    url = "https://passport.gds.org.cn/Account/Captcha"
    res = crawler_tools.request(session, 'GET', url, headers=headers, tojson=True, timeout=timeout, name='Captcha')
    image = base64.b64decode(res['Base64'])
    result = ocr.classification(image)
    logger.info(f"result: {result}")

    calculate = re.search(r'(.*?)([加减\-+])(.*)', result)
    if calculate:
        a1, op, a2 = calculate.groups()
        try:
            v1 = int(a1) if a1.isdigit() else cn2an.cn2an(a1)
            v2 = int(a2) if a2.isdigit() else cn2an.cn2an(a2)
            result = str(v1 + v2) if op in ('+', '加') else str(v1 - v2)
        except:
            pass

    return res['Id'], result


def login(session, params, codekey, verCode, __RequestVerificationToken, username, password):
    url = "https://passport.gds.org.cn/Account/Login"
    data = {
        "ReturnUrl": f'/connect/authorize/callback?{urlencode(params)}',
        "Type": "account",
        "Button": "login",
        "data": "",
        "username": username,
        "password": password,
        "phone": "",
        "phoneVer": "",
        "barCode": "",
        "passwordBar": "",
        "codekey": codekey,
        "verCode": verCode,
        "__RequestVerificationToken": __RequestVerificationToken
    }
    res = crawler_tools.request(session, 'POST', url, headers=headers, data=data, tojson=True, timeout=timeout, name='Login')
    logger.info(res)
    return res


def connect_authorize_callback(session, params):
    url = "https://passport.gds.org.cn/connect/authorize/callback"
    # res = crawler_tools.request(session, 'GET', url, headers=headers, params=params, toRaw=True, allow_redirects=False)
    res = session.get(url, headers=headers, params=params, allow_redirects=False, timeout=timeout, verify=False)
    location = res.headers['Location']
    code = re.search(r"code=(.*?)&", location).group(1)

    session.cookies.update(
        {
            "refreshToken_v4": "undefined",
            "accessToken_v4": "undefined",
            "tokenExpire_v4": "undefined",
            "refreshtime_v4": "Thu%20Jan%2001%201970%2007:59:59%20GMT+0800%20(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)",
            "currRoute_v4": "/home/<USER>"
        }
    )

    res = crawler_tools.request(session, 'GET', location, headers=headers, toRaw=True, timeout=timeout, name='location')
    res.encoding = 'utf-8'
    return code


def token(session, code, code_verifier):
    url = "https://passport.gds.org.cn/connect/token"
    data = {
        "client_id": "vuejs_code_client",
        "code": code,
        "redirect_uri": "https://www.gds.org.cn/#/callback",
        "code_verifier": code_verifier,
        "grant_type": "authorization_code"
    }
    res = crawler_tools.request(session, 'POST', url, headers=headers, data=data, tojson=True, timeout=timeout, name='token')
    return res


def get_user_info(session, access_token):
    global headers
    headers.update(
        {
            "Authorization": f"Bearer {access_token}",
            "Origin": "https://www.gds.org.cn",
        }
    )
    url = "https://passport.gds.org.cn/connect/userinfo"
    res = crawler_tools.request(session, 'GET', url, headers=headers, cookies={}, tojson=True)
    return res


def check_session(session, access_token, user_info: str):
    url = "https://passport.gds.org.cn/connect/checksession"
    headers_ = copy.deepcopy(headers)
    del headers_['Authorization']
    session.cookies.update({
        "accessToken": access_token,
        # "userInfo": user_info.replace("暂无信息", "")
        "userInfo": user_info.encode('utf-8').decode('latin-1')
    })
    res = crawler_tools.request(session, 'GET', url, headers=headers_, cookies=session.cookies)
    logger.info('check_session: ', res)


def get_user_is_auth(session):
    url = "https://bff.gds.org.cn/gds/carding-api/Cards/GetUserIsAuth"
    res = crawler_tools.request(session, 'GET', url, headers=headers, cookies={}, tojson=True, timeout=timeout, name='GetUserIsAuth')
    logger.info(res)


phones = [
    '19217287940',
    '16550987431',
    '19297002147',
    '19202669434',
    '16594800489',
    '19253531731',
    '16521771105',
    '19272484026',
    '19293462322',
    '16534190662'
]


def gds_login(username="16534190662", password="Ssjj5@0999"):
    for _ in range(2):
        try:
            session = requests.session()
            session.proxies = {
                'http': 'http://10.99.138.95:30636',
                'https': 'http://10.99.138.95:30636'
            }
            session.mount('http://', HTTPAdapter(max_retries=2))
            session.mount('https://', HTTPAdapter(max_retries=2))

            state_ = get_uuid().replace("-", "")
            code_verifier_ = (get_uuid() + get_uuid() + get_uuid()).replace("-", "")
            m = sha256_digest(code_verifier_)
            code_challenge_ = hex_to_base64url(m)

            params_ = {
                "client_id": "vuejs_code_client",
                "redirect_uri": "https://www.gds.org.cn/#/callback",
                "response_type": "code",
                "scope": "openid profile api1 offline_access",
                "state": state_,
                "code_challenge": code_challenge_,
                "code_challenge_method": "S256",
                "response_mode": "query"
            }

            __RequestVerificationToken_ = authorize(session, state_, code_challenge_)
            while True:
                Id_, ocr_result = get_pic(session)
                time.sleep(0.5)
                a: dict = login(session, params_, Id_, ocr_result, __RequestVerificationToken_, username, password)
                if a.get('Msg', '') == 'Success':
                    break

            code_ = connect_authorize_callback(session, params_)
            tokens_ = token(session, code_, code_verifier_)
            user_info_ = get_user_info(session, tokens_['access_token'])
            # check_session(tokens['access_token'], user_info_["UserInfo"])
            get_user_is_auth(session)
            return tokens_
        except Exception as e:
            logger.error('Error during GDS login:', crawler_tools.custom_traceback(e))


if __name__ == '__main__':
    # username = "15616431767"
    # username = "17673355057"
    # password = "DingShuJie5@0"
    # password = "DingShuJie5@0"
    crawler_tools = CrawlerTools()
    gds_login()

'''
expires_in_v4
expires_at - parseInt(Date.now() / 1000)

refreshtime_v4 
tokenExpire_v4 + 20minus

tokenExpire_v4
n = new Date
new Date(n.setSeconds(n.getSeconds() + expires_in))

expires_in
expires_at - parseInt(Date.now() / 1000)

expires_at
parseInt(Date.now() / 1000) + 7200
'''
