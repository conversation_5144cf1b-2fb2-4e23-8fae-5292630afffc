# encoding=utf8

import enum
import json
from typing import Optional, Dict, Any
from pydantic import Field, BaseModel as PydanticBaseModel
from resx.base_model import BaseModel as ResxBaseModel  # Assuming resx.base_model.BaseModel is the intended base
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class StatusCode(int, enum.Enum):
    SUCCESS = 0
    GENERAL_ERROR_RETRY = 1  # 通用错误,重试3次
    GIVE_UP = 2  # 爬虫放弃抓取，不重试
    OTHER_ERROR_RETRY_3 = 3  # 其他错误，重试3次
    OTHER_ERROR_RETRY_4 = 4  # 其他错误，重试3次
    OTHER_ERROR_RETRY_5 = 5  # 其他错误，重试3次
    OTHER_ERROR_RETRY_6 = 6  # 其他错误，重试3次
    SEARCH_EMPTY_NO_RETRY = 11  # 搜索无结果，不重试
    FEEDBACK_TIMEOUT = 99  # 回收超时，由feedback模块填写
    UNPROCESSED = -1  # 阶段未处理或未启动


class BaseLog(ResxBaseModel):  # Or PydanticBaseModel if resx.base_model is not available/intended
    code: StatusCode = Field(default=StatusCode.UNPROCESSED)
    error_msg: Optional[str] = Field(default="")
    receive_time: Optional[str] = Field(default=None)  # Using str for datetime as per example
    send_time: Optional[str] = Field(default=None)  # Using str for datetime as per example
    model: bool = Field(default=False)  # 为true时代表跳过该模块

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class SelectorLog(ResxBaseModel):  # Or PydanticBaseModel
    reason: str
    crawler_id: str
    crawler_name: str
    dimension_name: str
    score: float
    from_cache: bool
    info: Optional[Dict[str, Any]] = Field(default=None)
    receive_time: str
    send_time: str

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_info(self, key) -> Optional:
        if self.info is None:
            return None
        return self.info.get(key, None)


class CrawlerLog(BaseLog):
    raw_content: Optional[str] = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ParserLog(BaseLog):
    data: Optional[Dict[str, Any]] = Field(default={})

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class FusionDiffDetail(ResxBaseModel):  # Or PydanticBaseModel
    before: Any
    after: Any

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class FusionLog(BaseLog):
    diff: Optional[Dict[str, FusionDiffDetail]] = Field(default={})

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class ChannelLog(ResxBaseModel):  # Or PydanticBaseModel
    trace_id: Optional[str] = Field(default=None)
    source: Optional[str] = Field(default=None)
    debug_mode: Optional[bool] = Field(default=False)
    extra_headers: Optional[Dict[str, str]] = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class DimensionDetailLog(ResxBaseModel):  # Or PydanticBaseModel
    code: StatusCode = Field(default=StatusCode.UNPROCESSED)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class Eventlog(ResxBaseModel):  # Or PydanticBaseModel
    event_id: str
    code: StatusCode = Field(default=StatusCode.UNPROCESSED)
    selector: SelectorLog
    crawler: CrawlerLog = Field(default_factory=CrawlerLog)
    parser: ParserLog = Field(default_factory=ParserLog)
    fusion: FusionLog = Field(default_factory=FusionLog)
    channel: ChannelLog = Field(default_factory=ChannelLog)
    dimensions: Dict[str, DimensionDetailLog] = Field(default_factory=dict)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


if __name__ == '__main__':
    from resx.config import *
    from resx.redis_types import Redis

    redis = Redis(**CFG_REDIS_GS, db=9)
    task: list = redis.zpopmin('gds_company', count=1)
    a = task[0]

    eventlog = Eventlog.from_dict(json.loads(a[0]))
    print(eventlog)

    # from resx.config import *
    # from resx.redis_types import RedisQueue
    # from resx.config import CFG_REDIS_GS
    #
    # input_queue = RedisQueue(name='gds_barcode', **CFG_REDIS_GS, db=9)
    # a = json.loads(input_queue.pop())
    # eventlog = Eventlog.from_dict(a)
    # print(eventlog)
