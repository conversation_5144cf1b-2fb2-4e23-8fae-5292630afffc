import json
from datetime import datetime, date
from pydantic import Field, conint, field_serializer
from typing import Optional
from dateparser import parse as date_parse_fun
import re
import requests
import uuid
import time

from resx.base_model import BaseModel
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode
from eventlog_spider.parser.parser import Parser<PERSON><PERSON>, Parser, ParseTools
from resx.config import *
from resx.log import setup_logger
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from biz_utils.msv_write import msv_write_non_ic_company_base_info
from biz_utils.msv_write import MSVSource
from biz_utils.msv_write import msv_write_lawyers_info

logger = setup_logger(name=__name__)


class ParserLawTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)
        self.log_param_ctx = {'company': eventlog.selector.word, 'keyword': eventlog.selector.word}


class ParserLaw(Parser, ParseTools):
    @classmethod
    def get_name(cls):
        return 'law'

    def __init__(self):
        self.dao_law = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_ZX_RDS108,
            db_tb_name='data_judicial_risk.law_firm',
            primary_index_fields=(['creditCode'], []),
            entity_class=LawFirm,
            dim='律所基本信息',
            ignore_fields=['id', 'company_id'],
        )
        self.dao_lawyer = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_ZX_RDS108,
            db_tb_name='data_judicial_risk.lawyer',
            primary_index_fields=(['license'], []),
            entity_class=Lawyer,
            dim='律师',
            ignore_fields=['id', 'create_time'],
        )
        self.dao_lawyer2 = MySQLRWSplittingDao_(
            mysql_ro=CFG_MYSQL_ZX_RDS108,
            db_tb_name='data_judicial_risk.law_firm_team',
            primary_index_fields=(['License', 'graph_id'], []),
            entity_class=Lawyer2,
            dim='律师团队',
            ignore_fields=['id', 'create_time'],
        )
        self.dao_lawyer_history = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_ZX_RDS108,
            db_tb_name='data_judicial_risk.lawyer_history_law_firm',
            primary_index_fields=(['lawyer_id', 'law_firm_id'], []),
            entity_class=LawyerHistory,
            dim='律师历史律所',
            ignore_fields=['id', 'create_time'],
        )
        super().__init__(task_cls=ParserLawTask)

    def do_parse(self, *args, **kwargs):
        task: ParserLawTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        law_info: dict = json.loads(task.pages['law_info.json'])
        creditCode = law_info['creditCode']
        law_info2 = json.loads(task.pages['law_info2.json'])
        lawyer_list = json.loads(task.pages['lawyer_list.json'])
        lawyer_list2 = json.loads(task.pages['lawyer_list2.json'])
        ret = msv_write_lawyers_info(credit_no=creditCode, source=MSVSource.LAWYER_12348, items=lawyer_list2)
        logger.info(ret)
        change_fields2 = {}
        ret = msv_write_non_ic_company_base_info(credit_no=creditCode, source=MSVSource.LAW_ACLA, item=law_info)
        logger.info(ret)
        ret = msv_write_non_ic_company_base_info(credit_no=creditCode, source=MSVSource.LAW_12348, item=law_info2)
        logger.info(ret)
        law_info.update({'summary': law_info2.get('summary', ''), 'address_remarks': law_info2.get('address_remarks', '')})

        # 保存律所信息
        law_entity: LawFirm = LawFirm(**law_info)
        self.replace_fields_in_model(law_entity)
        law_pre: LawFirm = self.dao_law.get(creditCode=law_entity.creditCode)
        law_id = 0
        if law_pre:
            law_id = law_pre.id
            change_fields, change_fields2 = self.compare_print(law_entity.model_dump(), law_pre.model_dump(), law_entity.lawFirmName, law_entity.creditCode,
                                                               none_cover=True)
            for k, v in change_fields.items():
                setattr(law_pre, k, v)
            self.dao_law.save(law_pre, task.log_param_ctx)
        else:
            logger.info(f'insert law {law_entity.creditCode}')
            self.dao_law.save(law_entity, task.log_param_ctx)

        if not law_entity.creditCode.startswith('31'):
            return

        # 保存律师团队表
        gid = get_gid_by_nameAndCreditCode(law_entity.lawFirmName, law_entity.creditCode)
        delete_lawyers = self.delete_history(task, gid, lawyer_list, law_id)
        add_lawyers = []
        for lawyer in lawyer_list:
            lawyer_: Lawyer2 = Lawyer2(**lawyer)
            self.replace_fields_in_model(lawyer_)
            lawyer_.graph_id = gid
            try:
                if not lawyer.get('human_name') or not lawyer.get('License'):
                    logger.warning(f'没有人名或执业证号 name: {lawyer.get("human_name")} License: {lawyer.get("License")}')
                    continue
                self.fix_layer_year(lawyer_)
                lawyer_pre: Lawyer2 = self.dao_lawyer2.get(License=lawyer_.License, graph_id=gid)
                add_lawyer = self.save_lawyer(task, lawyer_pre, lawyer_, law_entity.lawFirmName, law_entity.creditCode)
                if add_lawyer:
                    add_lawyers.append(add_lawyer)
            except Exception as e:
                if 'Duplicate entry' not in str(e):
                    raise e
                logger.info(f'修复关联错误，以前发id中心只发了律所name')
                pre2 = self.dao_lawyer2.get(License=lawyer_.License, name=law_entity.lawFirmName, deleted=0)
                self.delete_more_lawyer_by_id([pre2['id']])
                self.save_lawyer(task, {}, lawyer_, law_entity.lawFirmName, law_entity.creditCode)

        # 律师表
        for lawyer in lawyer_list2:
            if not lawyer.get('name') or not lawyer.get('license'):
                logger.warning(f'没有人名或执业证号 name: {lawyer.get("name")} license: {lawyer.get("license")}')
                continue
            lawyer_: Lawyer = Lawyer(**lawyer)
            self.replace_fields_in_model(lawyer_)
            self.fix_layer_year(lawyer_)

            lawyer_pre: Lawyer = self.dao_lawyer.get(license=lawyer_.license)
            if '*' in lawyer_.license or len(lawyer_.license) not in [7, 13, 14, 15, 16, 17, 12]:
                continue
            if lawyer_pre:
                cc, _ = self.compare_print(lawyer_.model_dump(), lawyer_pre.model_dump(), law_entity.lawFirmName, law_entity.creditCode, lawyer_pre.name,
                                           lawyer_pre.license, False)
                for k, v in cc.items():
                    setattr(lawyer_pre, k, v)
                self.dao_lawyer.save(lawyer_pre, task.log_param_ctx)
            else:
                lawyer_.lawyer_id = str(uuid.uuid4()).replace('-', '')
                logger.info(f'新增: {lawyer_.model_dump()}')
                self.dao_lawyer.save(lawyer_, task.log_param_ctx)

        eventlog.spider.ab_info = {'change_fields': change_fields2, 'add_lawyers': add_lawyers, 'delete_lawyers': delete_lawyers} \
            if add_lawyers or delete_lawyers or change_fields2 else {}
        logger.info(f'解析完成: {law_entity.lawFirmName} {law_entity.creditCode} ab_info: {eventlog.spider.ab_info}')

    @staticmethod
    def replace_fields_in_model(model):
        """
        对模型的字符串类型字段执行替换操作：'干'替换为'千'，'侧'替换为'则'
        :param model: Pydantic模型实例
        """
        for field_name, field_value in model.__dict__.items():
            if isinstance(field_value, str):
                setattr(model, field_name, field_value.replace('干', '千').replace('则', '侧').replace('湛', '谌'))

    def delete_history(self, task: ParserLawTask, gid: int, lawyers: list[dict], law_id):
        """
        删除历史律师数据，以12348为准，地方不在此范围
        1. 律师离开现在律所
        """
        if not lawyers or not law_id:
            return []

        pre_all = self.dao_lawyer2.get_many(graph_id=gid, deleted=0)
        pre_all_12348 = [pre for pre in pre_all if pre.source == '12348']
        nameAndLicense = []
        for lawyer in lawyers:
            human_name = lawyer.get('human_name')
            License = lawyer.get('License')
            if not human_name or not License:
                return
            nameAndLicense.append(f'{human_name}-{License}')
        not_in_nameAndLicense = [item for item in pre_all_12348 if f'{item.human_name}-{item.License}' not in nameAndLicense]
        for item in not_in_nameAndLicense:
            logger.info(f'删除历史律师数据 name: {item.human_name} License: {item.License} pre_law_id: {law_id}')
            lawyer_one = self.dao_lawyer.get(license=item.License, deleted=0)
            self.dao_lawyer_history.save(LawyerHistory(lawyer_id=lawyer_one.lawyer_id, law_firm_id=law_id), task.log_param_ctx)
            self.dao_lawyer2.delete_logic({'id': item.id})
        return [i.human_name for i in not_in_nameAndLicense]

    def save_lawyer(self, task: ParserLawTask, lawyer_pre, lawyer_, law_name, creditCode):
        lawyer_dict = lawyer_.model_dump()
        if lawyer_pre:
            update_fields, _ = self.compare_print(lawyer_dict, lawyer_pre.model_dump(), law_name, creditCode, lawyer_dict['human_name'], lawyer_dict['License'],
                                                  False)
            for k, v in update_fields.items():
                setattr(lawyer_pre, k, v)
            self.dao_lawyer2.save(lawyer_pre, task.log_param_ctx)
        else:
            logger.info(f'新增数据 name: {law_name} creditCode: {creditCode} --> {lawyer_dict}')
            self.dao_lawyer2.save(lawyer_, task.log_param_ctx)
            return law_name
        return {}

    def compare_print(self, change_data: dict, old_data: dict, name: str = '', us_credit_code: str = '', lawyer_name='', License='', none_cover=True,
                      ignore_fields: tuple = (), is_print=True):
        if 'json_data' in change_data:
            del change_data['json_data']
        return super().compare_print(change_data, old_data, name, us_credit_code, lawyer_name, License, none_cover, ignore_fields, is_print)

    def delete_more_lawyer_by_id(self, law_firm_team_id: list[int]):
        for id in law_firm_team_id:
            self.dao_lawyer2.delete_logic({'id': id})

    @staticmethod
    def fix_layer_year(lawyer):
        if not lawyer.work_year or (lawyer.work_year and (int(lawyer.work_year) <= 0 or int(lawyer.work_year) > 80)):
            lawyer.work_year = ''


class LawyerHistory(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    lawyer_id: str = Field(default='')
    law_firm_id: conint(strict=True, ge=0) = Field(default=0)
    update_time: Optional[datetime] = Field(default=datetime.now())
    create_time: Optional[datetime] = Field(default=datetime.now())
    deleted: Optional[int] = Field(default=0)


class Lawyer2(BaseModel, ParseTools):
    id: conint(strict=True, ge=0) = Field(default=0)
    name: str = Field(default='')
    human_name: str = Field(default='')
    pic: Optional[str] = Field(default='')
    work_year: Optional[str] = Field(default='0')
    expertise: Optional[str] = Field(default='')
    License: str = Field(default='')
    post_num: Optional[str] = Field(default='')
    pic_url: Optional[str] = Field(default='')
    oss_path: Optional[str] = Field(default='')
    update_time: Optional[datetime] = Field(default=datetime.now())
    create_time: Optional[datetime] = Field(default=datetime.now())
    deleted: Optional[int] = Field(default=0)
    source: Optional[str] = Field(default='')
    graph_id: Optional[int] = Field(default=0)
    educational_background: Optional[str] = Field(default='')
    gender: Optional[str] = Field(default='')
    practice_area: Optional[str] = Field(default='')
    json_data: Optional[dict] = Field(default=None)

    def __init__(self, **data):
        if 'json_data' in data:
            data['json_data'] = json.loads(data['json_data']) if isinstance(data['json_data'], str) else data['json_data']
        super().__init__(**data)

    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime('%Y-%m-%d %H:%M:%S') if v else None,
        }

    @field_serializer('json_data')
    def dump_json_data(self, json_data: dict):
        if not json_data:
            return None
        return json.dumps(json_data, ensure_ascii=False)

    def to_dict(self) -> dict:
        return self.model_dump()


class Lawyer(BaseModel, ParseTools):
    id: conint(strict=True, ge=0) = Field(default=0)
    lawyer_id: str = Field(default='')
    name: str = Field(default='')
    license: str = Field(default='')
    work_year: Optional[str] = Field(default='0')
    expertise: Optional[str] = Field(default='')
    practice_area: Optional[str] = Field(default='')
    gender: Optional[str] = Field(default='')
    educational_background: Optional[str] = Field(default='')
    post_num: Optional[str] = Field(default='')
    pic_url: Optional[str] = Field(default='')
    oss_path: Optional[str] = Field(default='')
    source: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default=datetime.now())
    update_time: Optional[datetime] = Field(default=datetime.now())
    deleted: Optional[int] = Field(default=0)

    class Config:
        json_encoders = {
            datetime: lambda v: v.strftime('%Y-%m-%d %H:%M:%S') if v else None,
        }

    def to_dict(self) -> dict:
        return self.model_dump()


class LawFirm(BaseModel, ParseTools):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: Optional[conint(strict=True, ge=0)] = Field(default=0)
    lawFirmName: str = Field(default='')
    lawFirmAreaSign: Optional[str] = Field(default=None)
    name_en: Optional[str] = Field(default=None)
    telephone: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    fax: Optional[str] = Field(default=None)
    websit: Optional[str] = Field(default=None)
    address: Optional[str] = Field(default=None)
    address_remarks: Optional[str] = Field(default=None)
    postCode: Optional[str] = Field(default=None)
    dateOfEstablishment: Optional[str] = Field(default=None)
    summary: Optional[str] = Field(default=None)
    permit: Optional[str] = Field(default=None)
    dateOfIssue: Optional[str] = Field(default=None)
    unitProperty: Optional[str] = Field(default=None)
    have: Optional[str] = Field(default=None)
    creditCode: Optional[str] = Field(default='')
    legalPerson: Optional[str] = Field(default=None)
    organizationForm: Optional[str] = Field(default=None)
    authorities: Optional[str] = Field(default=None)
    approvalNumber: Optional[str] = Field(default=None)
    approvalDate: Optional[str] = Field(default=None)
    licensingOrganizations: Optional[str] = Field(default=None)
    taxRegistrationNumber: Optional[str] = Field(default=None)
    practiceState: Optional[str] = Field(default='正常执业')
    practiceState_remarks: Optional[str] = Field(default=None)
    creditRating: Optional[str] = Field(default=None)
    headquarters_Branch: Optional[str] = Field(default=None)
    registeredCapital: Optional[str] = Field(default=None)
    officeSize: Optional[str] = Field(default=None)
    officeState: Optional[str] = Field(default=None)
    partner: Optional[str] = Field(default=None)
    partner_count: Optional[str] = Field(default=None)
    practicingLawyer: Optional[str] = Field(default=None)
    lawDirector: Optional[str] = Field(default=None)
    lawDirector_telephone: Optional[str] = Field(default=None)
    fullTimeLawyer: Optional[str] = Field(default=None)
    fullTimeLawyer_count: Optional[str] = Field(default=None)
    partTimeLawyer: Optional[str] = Field(default=None)
    partTimeLawyer_count: Optional[str] = Field(default=None)
    stationedLawyer: Optional[str] = Field(default=None)
    apprenticeLawyer: Optional[str] = Field(default=None)
    executive: Optional[str] = Field(default=None)
    officials: Optional[str] = Field(default=None)
    theLeadingMemberOfTheConsortium: Optional[str] = Field(default=None)
    classicCase: Optional[str] = Field(default=None)
    scopeOfBusiness: Optional[str] = Field(default=None)
    rewardsAndPunishment: Optional[str] = Field(default=None)
    businessExpertise: Optional[str] = Field(default=None)
    annualAssessment: Optional[str] = Field(default=None)
    telephoneOfBusiness: Optional[str] = Field(default=None)
    annualSurvey: Optional[str] = Field(default=None)
    legalAidTimes: Optional[str] = Field(default=None)
    workPerformance: Optional[str] = Field(default=None)
    complaintsHotline: Optional[str] = Field(default=None)
    acceptingDepartment: Optional[str] = Field(default=None)
    abode: Optional[str] = Field(default=None)
    partyBranchState: Optional[str] = Field(default=None)
    partyBranchLeader: Optional[str] = Field(default=None)
    paralegal: Optional[str] = Field(default=None)
    officeSpace: Optional[str] = Field(default=None)
    InformationChange: Optional[str] = Field(default=None)
    complaints: Optional[str] = Field(default=None)
    cancellation: Optional[str] = Field(default=None)
    update_time: Optional[datetime] = Field(default=datetime.now())
    is_lawfirm: Optional[int] = Field(default=0)

    def __init__(self, **data):
        super().__init__(**data)
        self.lawFirmAreaSign = self.get_base_by_credit_code(self.creditCode)
        self.lawFirmName = self.replace_bracket(self.lawFirmName)
        # 对所有字符串类型字段执行strip()方法
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, str):
                setattr(self, field_name, field_value.strip())
        # 校验注册资金
        if self.registeredCapital and not re.search('\d+', self.registeredCapital):
            self.registeredCapital = ''
        # 校验日期
        if isinstance(self.dateOfIssue, str) and date_parse_fun(self.dateOfIssue):
            self.dateOfIssue = date_parse_fun(self.dateOfIssue).strftime('%Y-%m-%d')
        if isinstance(self.dateOfEstablishment, str) and date_parse_fun(self.dateOfEstablishment):
            self.dateOfEstablishment = date_parse_fun(self.dateOfEstablishment).strftime('%Y-%m-%d')
        if isinstance(self.approvalDate, str) and date_parse_fun(self.approvalDate):
            self.approvalDate = date_parse_fun(self.approvalDate).strftime('%Y-%m-%d')
        # 过滤兵团的主管机构数据错误问题
        if self.authorities and re.match('^[A-Z0-9a-z\-]{18}$', self.authorities):
            self.authorities = ''

    class Config:
        populate_by_name = True
        json_encoders = {datetime: lambda dt: dt.strftime('%Y-%m-%d %H:%M:%S')}

    def to_dict(self) -> dict:
        return self.model_dump()


class MySQLRWSplittingDao_(MySQLRWSplittingDao):
    def delete_logic(self, condition: dict):
        for i in range(1, 10):
            try:
                sql = f"update {self.mysql_dao_rw.db_tb_name} set deleted = {i} where {' and '.join([f'{k}=%s' for k in condition.keys()])}"
                args = list(condition.values())
                return self.execute(sql, args)
            except Exception:
                logger.error(f'delete_logic error {i}')


def get_gid_by_nameAndCreditCode(company_name, creditCode: str):
    """
    ID中心
    :param company_name:
    :param creditCode:
    :return: (company_id, graph_id)
    """
    num = 20
    num_ = 0
    while num:
        num -= 1
        try:
            creditCode = creditCode.upper()
            url = "http://idcenter-gsdata.jindidata.com/gsxt/idCenter/query"
            payload = json.dumps({
                "source": "test",
                "isUseName": True,
                "isUseHistoryName": True,
                "isShowGid": "true",
                "queryMeta": {
                    "name": company_name,
                    "creditCode": creditCode
                }
            })
            headers = {'Content-Type': 'application/json'}
            response = requests.request("POST", url, headers=headers, data=payload)

            if response.status_code == 200 and response.json().get('data'):
                data = response.json().get('data').get("hitEntityList")
                if not data:
                    raise Exception(f'{company_name}-{creditCode}-查询id中心无gid')
                return data[0]['companyGid']
                # return data[0]
        except Exception as e:
            if '查询id中心无gid' in str(e):
                if num_ >= 3:
                    raise e
                time.sleep(1)
                num_ += 1
                continue
            logger.warning(f'{company_name}-{creditCode}-获取gid失败:{e}')
