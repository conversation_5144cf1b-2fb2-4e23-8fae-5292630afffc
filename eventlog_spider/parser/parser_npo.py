import json
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional

from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_unify import Eventlog
from eventlog_spider.parser.parser import ParserTask, Parser, ParseTools
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from resx.config import *
from resx.log import setup_logger
from biz_utils.msv_write import msv_write_non_ic_company_base_info
from biz_utils.msv_write import MSVSource

logger = setup_logger(name=__name__)


class ParserNpoTask(ParserTask):
    pass


class ParserNpo(Parser, ParseTools):
    @classmethod
    def get_name(cls):
        return 'npo'

    def __init__(self):
        self.dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.npo',
            primary_index_fields=(['unified_social_credit_code'], []),
            entity_class=Npo,
            dim='社会组织基本信息',
            ignore_fields=['organization_code', 'website', 'phone', 'contact_people', 'registration_number', 'industry_category', 'post_code', 'source',
                           'property1', 'property2', 'id', 'company_id', 'update_time'],
        )
        self.change_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.npo_change_info',
            primary_index_fields=(['npo_id'], ['business_type', 'change_time']),
            dim='社会组织变更信息',
            ignore_fields=[''],
        )
        self.check_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.npo_annual_check_result',
            primary_index_fields=(['npo_id'], ['year']),
            dim='社会组织年检',
            ignore_fields=[''],
        )
        self.evaluate_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.npo_evaluation_info',
            primary_index_fields=(['npo_id'], ['rating']),
            dim='社会组织评估信息',
            ignore_fields=[''],
        )
        self.comment_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.npo_honorary_info',
            primary_index_fields=(['npo_id'], ['honorary_name']),
            dim='社会组织评估信息',
            ignore_fields=[''],
        )
        self.status_mapping = {'0': None, '1': '正常', '2': '注销', '3': '撤销'}
        self.types_mapping = {"1": "社会团体", "2": "民办非企业单位", "3": "基金会", "9": "其它"}
        self.abnormalActivity_mapping = {
            "600571": "未按照规定时限向登记管理机关报送年度工作报告",
            "600572": "未按照规定要求向登记管理机关报送年度工作报告",
            "600573": "未按照有关规定设立党组织",
            "600574": "登记管理机关在抽查和其他监督检查中发现问题，发放整改文书要求限期整改，社会组织未按期完成整改",
            "600575": "具有公开募捐资格的慈善组织不再符合公开募捐资格条件",
            "600576": "具有公开募捐资格的慈善组织6个月以上不开展公开募捐活动",
            "600577": "受到警告处罚",
            "600578": "通过登记的住所无法与社会组织取得联系",
            "600579": "法律、行政法规规定应当列入的其他情形",
            "600580": "受到不满5万元罚款处罚",
            "600581": "未按照规定时限和要求向登记管理机关报送年度工作报告的",
            "600582": "具有公开募捐资格的慈善组织，存在《慈善组织公开募捐管理办法》第二十一条规定情形的",
            "600583": "受到警告或者不满5万元罚款处罚的",
        }
        self.res3 = {"Z02": "社会团体注销", "S01": "基金会成立", "S07": "涉外基金会成立", "S05": "国际性社团成立",
                     "S06": "外国商会成立", "S04": "境外基金会代表机构成立", "Z03": "民办非企业单位注销",
                     "Z01": "基金会注销", "Z07": "涉外基金会注销", "Z05": "国际性社团注销", "Z06": "外国商会注销",
                     "Z04": "境外基金会代表机构注销", "S02": "社会团体成立", "S03": "民办非企业单位成立"}
        self.res4 = {"B21": "名称变更", "B22": "法定代表人变更", "B23": "业务主管单位变更", "B24": "住所变更",
                     "B25": "活动资金变更", "B26": "章程核准变更",
                     "B27": "活动地域变更", "B28": "业务范围变更", "B29": "注册资金变更", "B30": "负责人变更",
                     "B31": "章程变更"}
        self.res5 = {'B21': '名称变更', 'B22': '法定代表人变更', 'B23': '业务主管单位变更', 'B24': '住所变更', 'B25': '活动资金变更', 'B26': '章程核准变更',
                     'B27': '活动地域变更', 'B28': '业务范围变更', 'B29': '注册资金变更', 'B30': '负责人变更', 'B31': '章程变更'}
        self.res6 = {'0': '未参检', '1': '合格', '2': '基本合格', '3': '不合格'}
        self.res7 = {'1': '1A级（A）', '2': '2A级（AA）', '3': '3A级（AAA）', '4': '4A级（AAAA）', '5': '5A级（AAAAA）'}

        super().__init__(task_cls=ParserNpoTask)

    def do_parse(self, *args, **kwargs):
        task: ParserNpoTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        reg_cer_info = json.loads(task.pages['registration_certificate_info.json'])

        def to_(filed):
            return filed if filed else '-'

        expiry_date = f'{to_(reg_cer_info.get("aaae0703", None))} 至 {to_(reg_cer_info.get("aaae0704", None))}'
        if expiry_date == '- 至 -':
            expiry_date = ''

        data_dict = {
            'name': reg_cer_info.get('aaae0103', '').strip().replace('&rdquo;', '”').replace('&ldquo;', '“'),  # 名称
            'base': self.get_base_by_credit_code(reg_cer_info.get('aaae0102', '')),  # 地区
            'unified_social_credit_code': reg_cer_info.get('aaae0102', ''),  # 统一社会信用代码
            'registration_authority': reg_cer_info.get('aaae0107', ''),  # 登记管理机关
            'business_unit': reg_cer_info.get('aaae0109') if reg_cer_info.get('aaae0109', '') else reg_cer_info.get('aaae0111', ''),  # 业务主管单位
            'legal_person': reg_cer_info.get('aaae0113', ''),  # 法人
            'registration_date': reg_cer_info.get('aaae0123', ''),  # 成立登记日期
            'expiry_date': expiry_date,  # 有效期
            'registered_capital': f'{reg_cer_info.get("aaae0129", "")}万元',  # 注册资金
            'status': self.status_mapping.get(reg_cer_info.get('aaae0127', ''), ''),  # 登记状态
            "types": self.types_mapping.get(reg_cer_info.get('aaae0105', ''), ''),  # 社会组织类型
            "address": reg_cer_info.get('aaae0116', ''),  # 住所
            "business_scope": reg_cer_info.get('aaae0122', ''),  # 业务范围
            "property": 'new_country_npo',  # 未知字段 2020/11/5定义为【来源以分号分割】（history, china, gd_local, js_local, qxb）
            "has_raise_funds_qualification": 1 if reg_cer_info.get('aaae0137', '') == '1' else 0,  # 是否有募捐资格
            "is_charity": 1 if reg_cer_info.get('aaae0139', '') == '1' else 0,  # 是否慈善组织
        }
        ret = msv_write_non_ic_company_base_info(credit_no=data_dict['unified_social_credit_code'], source=MSVSource.NPO_GJ, item=data_dict)
        logger.info(ret)
        npo_entity: Npo = Npo.from_dict(data_dict)

        info_pre: Npo = self.dao.get(unified_social_credit_code=npo_entity.unified_social_credit_code)
        if info_pre:
            _, eventlog.fusion.diff = self.compare_print(npo_entity.model_dump(), info_pre.model_dump(), data_dict['name'],
                                                         data_dict['unified_social_credit_code'],
                                                         ignore_fields=('organization_code', 'website', 'phone', 'contact_people', 'registration_number',
                                                                        'industry_category', 'post_code', 'property1', 'property2'))
        else:
            logger.info(f'insert npo {npo_entity.unified_social_credit_code}')
        self.dao.save(npo_entity, task.log_param_ctx)
        npo_id = self.dao.get(unified_social_credit_code=npo_entity.unified_social_credit_code).id

        change_info = json.loads(task.pages['change_info.json'])
        changes = []
        for i in change_info:
            changes.append({
                'npo_id': npo_id,
                'business_type': self.res5[i['aaae1302']],
                'change_before': i['aaae1304'],
                'change_affter': i['aaae1305'],
                'change_time': i['aaae2903']
            })
        self.change_dao.save_group(changes, group_index_values=[npo_id])

        year_check_info = json.loads(task.pages['year_check_info.json'])
        checks = [
            {
                'npo_id': npo_id,
                'year': i['aaae1202'],
                'result': self.res6[i['aaae1203']]
            } for i in year_check_info
        ]
        self.check_dao.save_group(checks, group_index_values=[npo_id])

        evaluate_info = json.loads(task.pages['evaluate_info.json'])
        evaluates = [
            {
                'npo_id': npo_id,
                'rating': self.res7[i['aaae1404']],
                'validity_date': f'{i["aaae0703"]}至{i["aaae0704"]}',
                'evaluate_status': '正常' if i['aaae1405'] == '1' else '过期'
            } for i in evaluate_info
        ]
        self.evaluate_dao.save_group(evaluates, group_index_values=[npo_id])

        commend_info = json.loads(task.pages['commend_info.json'])
        commends = [
            {
                'npo_id': npo_id,
                'honorary_name': i['aaae2202'],
                'date': i['aaae2206'],
                'organization': i['aaae2205'] or ""
            } for i in commend_info
        ]
        self.comment_dao.save_group(commends, group_index_values=[npo_id])

        logger.info(f'解析成功: {npo_entity.name} {npo_entity.unified_social_credit_code} ab_info: {eventlog.fusion.diff}')

        """
        aaae0127 --> 1 正常 - 2 注销 - 3 撤销
        aaae0136 --> 是否慈善组织
        aaae0139 --> 是否有公开募捐资格
        aaae0137 --> 志愿服务组织
        aaae0149 --> 行业协会商会
        aae20having --> 被列入活动异常名录
        aae21having --> 被列入严重违法失信名单
        """


class Npo(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: Optional[int] = Field(default=0)
    name: str = Field(default='')
    base: Optional[str] = Field(default='')
    unified_social_credit_code: Optional[str] = Field(default='')
    organization_code: Optional[str] = Field(default='')
    registration_authority: Optional[str] = Field(default='')
    business_unit: Optional[str] = Field(default='')
    legal_person: Optional[str] = Field(default='')
    registration_date: Optional[date]
    expiry_date: Optional[str] = Field(default='')
    registered_capital: Optional[str] = Field(default='')
    status: Optional[str] = Field(default='')
    website: Optional[str] = Field(default='')
    phone: Optional[str] = Field(default='')
    contact_people: Optional[str] = Field(default='')
    registration_number: Optional[str] = Field(default='')
    types: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    industry_category: Optional[str] = Field(default='')
    business_scope: Optional[str] = Field(default='')
    post_code: Optional[str] = Field(default='')
    source: Optional[str] = Field(default='')
    crawl_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
    property: Optional[str] = Field(default='')
    has_raise_funds_qualification: Optional[int] = Field(default=0)
    is_charity: Optional[int] = Field(default=0)
    property1: Optional[str] = Field(default='')
    property2: Optional[str] = Field(default='')
    deleted: Optional[int] = Field(default=0)
