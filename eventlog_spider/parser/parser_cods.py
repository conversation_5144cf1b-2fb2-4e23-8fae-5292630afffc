import json
import re
from datetime import datetime, date
from typing import Optional, Union
from pydantic import Field, conint
from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_unify import Eventlog
from eventlog_spider.parser.parser import Pa<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ParseTools, check_info
from resx.mysql_dao import MySQLDao
from resx.config import *
from resx.log import setup_logger
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from biz_utils.msv_write import msv_write_non_ic_company_base_info
from biz_utils.msv_write import MSVSource

logger = setup_logger(name=__name__)


class CodsParserTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)
        self.log_param_ctx = {'company': check_info(eventlog), 'keyword': check_info(eventlog)}


class CodsParser(<PERSON><PERSON>r, ParseTools):

    @classmethod
    def get_name(cls):
        return 'cods'

    def __init__(self):
        self.cods_dao = CodsDao(db_tb_name='internal.cods_company', **CFG_MYSQL_GS_INNER)
        self.cods_dao2 = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.organization_info',
            primary_index_fields=(['unified_social_credit_code'], []),
            entity_class=CodsEntity,
            dim='Cods',
            ignore_fields=['id', 'company_id', 'company_gid', 'create_time'],
        )
        super().__init__(task_cls=CodsParserTask)

    def do_parse(self):
        task: CodsParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages

        info = pages.get('info.txt', '')
        if not info:
            logger.warning(f'no info {eventlog}')
            return
        info = json.loads(info)

        self.cods_dao.update_jgdm(info['jgdm'], info['统一社会信用代码'])
        legal_person = info.get('法定代表人或负责人姓名', '')
        registration_date = re.search(r'(\d{4}-\d{2}-\d{2})', info.get('成立日期', ''))

        cods_entity: CodsEntity = CodsEntity.from_dict({
            "org_name": info['name'],
            'org_province': self.get_base_by_credit_code(info['统一社会信用代码']),
            "unified_social_credit_code": info['统一社会信用代码'],
            "registration_authority": info.get('登记管理部门名称', '') or info.get('批准机构名称', ''),
            "business_unit": info.get('经济行业', ''),
            "legal_person": legal_person,
            "registration_date": datetime.strptime(registration_date.group(1), '%Y-%m-%d') if registration_date else '0000-00-00',
            "expiry_date": info.get('经营期限', '').replace('到', '至'),
            "registered_capital": info['注册资本'] if info.get('注册资本', '') not in ['-', '', '--'] else '',
            "reg_status": info.get('status', ''),
            "registration_number": info.get('登记号', ''),
            "org_types": info.get('机构类型', ''),
            "address": info.get('注册地址', ''),
            "industry_category": info.get('经济类型', ''),
            "business_scope": info.get('经营范围', ''),
            "org_source": 'cods',
        })
        ret = msv_write_non_ic_company_base_info(credit_no=cods_entity.unified_social_credit_code, source=MSVSource.CODS,
                                                 item=self.remove_no_use(cods_entity.model_dump(mode='json')))
        logger.info(ret)

        pre = self.cods_dao2.get(unified_social_credit_code=info['统一社会信用代码'])
        self.cods_dao2.save(cods_entity, task.log_param_ctx)
        if pre:
            _, eventlog.fusion.diff = self.compare_print(cods_entity.model_dump(), pre.model_dump(), info['name'], info['统一社会信用代码'])
        elif re.search('^(11|13|19|32|33|34|35|39|41|49|54|55|59|61|62|69|71|72|79|80|81|89|G1|N1|N2|N3|N9|Y1).{16}', cods_entity.unified_social_credit_code):
            logger.info(f'insert cods {cods_entity.unified_social_credit_code}')
        logger.info(f'解析成功: {cods_entity.unified_social_credit_code} {cods_entity.org_name} ab_info: {eventlog.fusion.diff}')


class CodsDao(MySQLDao):
    def update_jgdm(self, jgdm, unified_social_credit_code):
        sql = (f"insert into {self.db_tb_name} (property1, property4) values (%s, %s) "
               f"on duplicate key update property4 = values(property4)")
        return self.execute(sql, args=[unified_social_credit_code, jgdm])


class CodsEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_gid: conint(strict=True, ge=0) = Field(default=0)
    company_id: conint(strict=True, ge=0) = Field(default=0)
    org_name: Optional[str] = Field(default='')
    org_province: Optional[str] = Field(default='')
    unified_social_credit_code: Optional[str] = Field(default='')
    registration_authority: Optional[str] = Field(default='')
    business_unit: Optional[str] = Field(default='')
    legal_person: Optional[str] = Field(default='')
    legal_person_id: int = Field(default=0)
    registration_date: Union[date, str] = Field(default='0000-00-00')
    expiry_date: Optional[str] = Field(default='')
    registered_capital: Optional[str] = Field(default='')
    reg_status: Optional[str] = Field(default='')
    website: Optional[str] = Field(default='')
    phone: Optional[str] = Field(default='')
    registration_number: Optional[str] = Field(default='')
    org_types: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    industry_category: Optional[str] = Field(default='')
    business_scope: Optional[str] = Field(default='')
    org_source: Optional[str] = Field(default='')
    deleted: int = Field(default=0)
    create_time: Optional[datetime] = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: Optional[datetime] = Field(default_factory=lambda: datetime.now().replace(microsecond=0))

    class Config:
        json_encoders = {datetime: lambda dt: dt.strftime('%Y-%m-%d %H:%M:%S')}
